# GPayo Bills Payment Fallout Cron Job

A Node.js cron job service that processes failed bill payments and generates fallout files for reconciliation with Globe's billing systems.

## Overview

This service identifies failed payment postings, processes them by account type and payment method, generates encrypted fallout files, and uploads them to SFTP for further processing by downstream systems.

## Architecture & Flow

```mermaid
flowchart TD
    A[Cron Job Trigger] --> B[BillsFalloutService.execute]
    B --> C[Get Failed Transactions]
    C --> D[FailedTransactionsRepository]
    D --> E[Process by Account Type]
    E --> F[TransactionLogsRepository<br/>Get Payment Method & Gateway]
    F --> G[Group by Account Type,<br/>Customer Type & Payment Method]
    G --> H[Generate Fallout Files]
    H --> I[Create File Stream]
    I --> J[PGP Encrypt Stream]
    J --> K[Upload to SFTP]
    K --> L[Update Transaction Status]
    L --> M[SettlementRepository<br/>Update to POSTED_BATCHFILE]
    L --> N[FailedTransactionsRepository<br/>Update Status & Batch File]
    L --> O[EventLogsRepository<br/>Log POSTING_BATCHFILE Event]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style D fill:#fff3e0
    style F fill:#fff3e0
    style K fill:#e8f5e8
    style M fill:#fff3e0
    style N fill:#fff3e0
    style O fill:#fff3e0
```

## Processing Flow Details

### 1. Failed Transaction Retrieval
- Queries `FailedTransactionsRepository` for transactions with status `POSTING_FAILED`
- Uses pagination to handle large datasets
- Processes transactions in batches for optimal performance

### 2. Transaction Processing
- **Account Type Mapping**: Maps transactions to Globe account types (Globe, TM, etc.)
- **Customer Type Classification**: Categorizes as corporate (`corp`) or regular customers
- **Payment Method Grouping**: Groups by payment methods (card, gcash, paymaya, etc.)
- **Data Enrichment**: Fetches additional data from `TransactionLogsRepository`

### 3. Fallout File Generation
- **File Naming Convention**: `{accountKey}{YYYYMMDD}{batchNo}[crp].txt`
  - Batch numbers based on time: 01 (9AM-1PM), 02 (1PM-5PM), 03 (5PM-9PM), 04 (9PM-9AM)
  - Corporate files have `crp` suffix
- **Data Mapping**: Transforms transaction data to fallout file format
- **File Structure**: Fixed-width format with transaction details

### 4. Security & Upload
- **PGP Encryption**: Files are encrypted before upload using PGP encryption
- **SFTP Upload**: Secure file transfer to designated SFTP server
- **Error Handling**: Comprehensive error handling and logging

### 5. Status Updates
- **Settlement Updates**: Mark settlements as `POSTED_BATCHFILE`
- **Failed Transaction Updates**: Update status and add batch file reference
- **Event Logging**: Create audit trail with `POSTING_BATCHFILE` events
- **Atomic Operations**: Uses DynamoDB transactions for data consistency

## Services & Repositories

### Core Services
- **BillsFalloutService**: Main orchestration service
- **SftpService**: Handles secure file transfer operations

### Data Access Layer
- **FailedTransactionsRepository**: Manages failed payment transactions
- **TransactionLogsRepository**: Accesses transaction logs for payment details
- **SettlementRepository**: Handles settlement status updates
- **EventLogsRepository**: Manages audit events and logging

### Utility Services
- **PGP Encryption**: Encrypts files before upload
- **File Streaming**: Creates and manages file streams
- **Date Utilities**: Handles Philippine timezone date/time operations
- **UUID Generation**: Creates unique identifiers

## Configuration

### Environment Variables
- `FALLOUT_TARGET_PATH`: SFTP target directory path
- `DDB_TABLE_*`: DynamoDB table names
- Various SFTP and encryption configuration

### Account Type Mapping
```javascript
ACCOUNT_TYPE_MAPPING = {
  `G`: 'globe',
  `I`: 'innove',
  `N`: 'innove',
  `B`: 'bayan'
};
```

### Payment Method Processing
- **Card Payments**: `card_straight`, `card_installment` → grouped as `card`
- **E-wallets**: `gcash`, `paymaya`, etc.
- **Different deposit accounts** per payment method and account type

## File Format

Fallout files contain the following fields:
- Transaction Date
- Dealer Name (Account Name)
- Customer Account Number
- Transaction Amount
- Deposit Account Number
- Service Number
- Receipting Reference Number (Payment ID)
- Debit/Credit Indicator
- Cash/Check Indicator
- Payment Mode

## Error Handling

- **Batch Processing**: Processes transactions in configurable batch sizes
- **Chunked Updates**: Uses DynamoDB transaction limits (10 items per transaction)
- **Retry Logic**: Built-in retry mechanisms for external service calls
- **Comprehensive Logging**: Detailed logging for monitoring and debugging
- **Graceful Degradation**: Continues processing even if individual transactions fail

## Monitoring & Logging

- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Progress Tracking**: Batch processing progress indicators
- **Error Tracking**: Detailed error logs with stack traces
- **Performance Metrics**: Processing time and batch size metrics

## Installation

```bash
npm install
```

## Usage

```bash
# Run the fallout processing
npm start

# Run in development mode
npm run dev
```

## Dependencies

- **AWS SDK**: DynamoDB operations
- **Node.js Streams**: File processing
- **SSH2-SFTP-Client**: SFTP operations
- **OpenPGP**: File encryption
- **Pino**: Logging framework
