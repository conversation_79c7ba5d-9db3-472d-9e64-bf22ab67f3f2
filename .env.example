# ---------------------------------------------------------------- #
# ---------------------- DDB  ------------------------------------ #
# ---------------------------------------------------------------- #
DDB_AWS_REGION="ap-southeast-1"
DDB_MAX_ATTEMPTS=2
DDB_ENDPOINT="http://localstack:4566"
DDB_ENABLE_LOGGING=true
DDB_TABLE_TRANSACTION_LOGS="isg-gpayo-local-transLogs"
DDB_TABLE_TRANSACTIONS_LOGS_STATUS_INDEX="StatusIndex"
DDB_TABLE_CHANNEL="isg-gpayo-local-channels"
DDB_TABLE_EVENT_LOGS="isg-gpayo-local-eventLogs"
DDB_TABLE_SETTLEMENTS="isg-gpayo-local-settlements"
DDB_TABLE_FAILED_TRANS="isg-gpayo-local-failedTransaction"

#----------------------------------------------------#
#----------- CHANNEL CB CONFIGURATION ---------------#
#----------------------------------------------------#
CXS_CALLBACK_API_KEY=api_key

#----------------------------------------------------#
#----------- PGP CONFIGURATION ----------------------#
#----------------------------------------------------#
PGP_PUBLIC_KEY_PATH=.pgp/public_key.asc

#----------------------------------------------------#
#----------- SFTP CONFIGURATION ---------------------#
#----------------------------------------------------#
# Run .devcontainer/sftp/setup-sftp.sh after starting devcontainer
SFTP_HOST=localhost
SFTP_PORT=2222
SFTP_USERNAME=sftpuser
SFTP_PRIVATE_KEY_PATH=.devcontainer/sftp/ssh_keys/id_rsa

FALLOUT_TARGET_PATH=upload

# ---------------------------------------------------------------- #
# -------------------- APP SERVER CONFIGS ------------------------ #
# ---------------------------------------------------------------- #
LOG_LEVEL=info
NODE_ENV=local
