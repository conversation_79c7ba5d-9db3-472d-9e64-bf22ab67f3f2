{"name": "gpayo-bills-payment-fallout-cron-job", "version": "1.0.0", "description": "Globe Payo API", "main": "index.js", "engines": {"node": ">=20"}, "scripts": {"start": "node index.js", "dev": "NODE_ENV=local node index.js", "test": "jest --config jest.config.js --forceExit", "lint": "eslint '**/*.js' --fix", "format": "prettier '**/*.js' --write", "prepare": "husky"}, "author": "Globe Telecom", "license": "ISC", "dependencies": {"@aws-sdk/client-dynamodb": "3.682.0", "@aws-sdk/lib-dynamodb": "3.685.0", "aws-sdk": "2.1691.0", "axios": "1.8.4", "axios-logger": "2.8.1", "axios-retry": "4.5.0", "dotenv": "16.4.5", "env-schema": "6.0.0", "module-alias": "2.2.3", "openpgp": "6.1.1", "pino": "9.5.0", "pino-pretty": "11.3.0", "ssh2": "1.16.0", "uuid": "11.1.0"}, "_moduleAliases": {"@root": ".", "@configs": "src/configurations", "@utils": "src/utils", "@services": "src/services", "@repositories": "src/repositories", "@exception": "src/configurations/exception"}, "devDependencies": {"@eslint/js": "9.20.0", "eslint": "9.20.1", "eslint-config-prettier": "10.0.1", "globals": "15.15.0", "husky": "9.1.7", "jest": "29.7.0", "jest-sonar": "0.2.16", "lint-staged": "15.4.3", "module-alias-jest": "0.0.3", "prettier": "3.5.1"}, "lint-staged": {"**/*.js": ["eslint --fix --max-warnings 0 .", "prettier --write", "jest --bail --findRelatedTests"]}}