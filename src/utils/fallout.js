const { PAYBILL_CORP_CHANNEL_IDS } = require('@configs/env');

const ACCOUNT_TYPE_MAPPING = {
  G: 'globe',
  I: 'innove',
  N: 'innove',
  B: 'bayan'
};

const PAYMENT_MODES = {
  mc: 96,
  visa: 96,
  cc: 96,
  cc_dc: 75, // xendit ccdc
  card: 75,
  card_straight: 75,
  card_installment: 75,
  otc: 57,
  jcb: 96,
  dropin: 96,
  amex: 96,
  dragonpay_gcash: 53,
  bank: 92,
  paymaya: 72,
  grabpay: 73,
  shopeepay: 74,
  bpi: 76,
  ubp: 76,
  rcbc: 76,
  myb: 12,
  spb: 13,
  gbb: 16,
  ddb: 44,
  ccb: 49,
  gm_maya: 67,
  gm_shp: 68,
  gm_grab: 69,
  gm_dd: 80,
  gm_cc: 83,
  cci: 50 // Credit Card Installment
};

const DEPOSIT_ACCOUNT_NUMBERS = {
  card_straight: {
    innove: **********,
    globe: **********,
    bayan: **********
  },
  card_installment: {
    innove: **********,
    globe: **********,
    bayan: **********
  },
  dragonpay_gcash: {
    innove: **********,
    globe: **********,
    bayan: **********
  },
  default: {
    innove: **********,
    globe: **********,
    bayan: **********
  }
};

// T1 equivalent: ['cc_dc', 'gcash', 'bank', 'xendit'];
// const consumerPaymentMethods = ['card', 'gcash', 'xendit'];
// T1 equivalent : ['cc_dc', 'gcash']
// const corpPaymentMethods = ['card', 'gcash'];

const falloutFileNameKeys = {
  globe: {
    card: 'gyes',
    gcash: 'gogc',
    // bank: 'bbss',
    xendit: 'gxen'
  },
  innove: {
    card: 'iyes',
    gcash: 'iogc',
    // bank: 'ibss',
    xendit: 'ixen'
  },
  bayan: {
    card: 'byes',
    gcash: 'bogc',
    // bank: 'bpss',
    xendit: 'bxen'
  }
};

const getFalloutAccount = (
  falloutAccountMap,
  { accountType, customerType, paymentMethod }
) => {
  const fileNamePrefix = falloutFileNameKeys[accountType][paymentMethod];

  if (!fileNamePrefix) {
    return {
      success: false,
      message: 'Invalid account type or payment method'
    };
  }
  // { 
  //   'globe-corp-card': {
  //     key: 'gyescrp',
  //     stream: null,
  //     ids: [],
  //     rawData: [],
  //     mappedData: []
  // 'bayan-consumer-gcash': {
  //     key: 'gyescrp',
  //     stream: null,
  //     ids: [],
  //     rawData: [],
  //     mappedData: []
  // }
  const property = `${accountType}-${customerType}-${paymentMethod}`;
  if (!Object.hasOwn(falloutAccountMap, property)) {
    Object.assign(falloutAccountMap, {
      [property]: {
        key: fileNamePrefix + (customerType === 'corp' ? 'txt' : ''),
        stream: null,
        ids: [],
        rawData: [],
        mappedData: []
      }
    });
  }
  return {
    success: true,
    falloutAccount: falloutAccountMap[property]
  };
};

const getCustomerType = (channelId) => {
  // As of now, we determine customer type based on channel ID
  const corpChannelIds = PAYBILL_CORP_CHANNEL_IDS
    ? PAYBILL_CORP_CHANNEL_IDS.split(',')
    : [];

  return corpChannelIds.includes(channelId) ? 'corp' : 'consumer';
};

module.exports = {
  PAYMENT_MODES,
  ACCOUNT_TYPE_MAPPING,
  DEPOSIT_ACCOUNT_NUMBERS,
  getFalloutAccount,
  getCustomerType
};
