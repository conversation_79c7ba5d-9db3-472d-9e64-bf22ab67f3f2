const oneLineKeyToPemFormat = (str) => {
  if (str.includes('\n') || str.includes('\r')) {
    // Already multi-line, assume it's properly formatted
    return str;
  }

  // Extract BEGIN block
  const beginMatch = str.match(/-----BEGIN[^-]+-----\s/);
  if (!beginMatch) {
    throw new Error('Invalid PEM format: Missing BEGIN block');
  }
  const beginBlock = beginMatch[0];

  // Extract END block
  const endMatch = str.match(/-----END[^-]+-----/);
  if (!endMatch) {
    throw new Error('Invalid PEM format: Missing END block');
  }
  const endBlock = endMatch[0];

  // Extract Version block (if present) - look for "Version:" followed by content until double space
  const versionMatch = str.match(/Version:[^]+?\s{2,}/);
  const versionBlock = versionMatch ? versionMatch[0] : '';

  // Get the content between BEGIN and END blocks
  const beginIndex = str.indexOf(beginBlock) + beginBlock.length;
  const endIndex = str.indexOf(endBlock);
  let content = str.substring(beginIndex, endIndex);
  if (!content) {
    throw new Error('Invalid PEM format: Missing Content block');
  }

  // Remove version block from content if it exists
  if (versionBlock) {
    content = content.replace(versionBlock, '');
  }
  // Format the content (convert spaces to newlines)
  const formattedContent = content.replace(/\s/g, '\n');

  const replaceSpaceAtEndWithNewLine = (str) => {
    return str.replace(/(\s)+$/g, (match) => '\n'.repeat(match.length));
  };

  // Reassemble: BEGIN + VERSION + FORMATTED_CONTENT + END
  return (
    replaceSpaceAtEndWithNewLine(beginBlock) +
    replaceSpaceAtEndWithNewLine(versionBlock) +
    formattedContent +
    endBlock
  );
};

module.exports = { oneLineKeyToPemFormat };
