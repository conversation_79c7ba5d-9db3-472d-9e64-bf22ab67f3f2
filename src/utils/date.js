const PH_OFFSET_MS = 28_800_000;
const ONE_HOUR_MS = 3_600_000;

/**
 * Gets the current date-time in the Philippines timezone (UTC+8).
 * @returns {Date} A Date object representing the current PH time.
 */
const getPhDateTime = () => {
  return new Date(Date.now() + PH_OFFSET_MS);
};

/**
 * Gets the timestamp for today's date at 11:59:59 AM in the local timezone.
 * @returns {number} A timestamp (in milliseconds) for today's date at 11:59:59 AM.
 */
const getTodayTimestamp = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth(); // Note: Month is zero-indexed
  const date = now.getDate();

  // Create a new date for today at 11:59:59
  const targetDate = new Date(year, month, date, 11, 59, 59);
  return targetDate.getTime();
};

/**
 * Gets the current date-time in the Philippines timezone (UTC+8) as an ISO string.
 * @returns {string} ISO formatted string of the current PH time.
 */
const getPhDateTimeString = () => {
  return new Date(Date.now() + PH_OFFSET_MS)
    .toISOString()
    .replace('Z', '+08:00');
};

/**
 * Gets the date-time of one hour before the current time in the Philippines timezone (UTC+8) as an ISO string.
 * @returns {string} ISO formatted string of the PH time one hour ago.
 */
const getPreviousOneHourPhTime = () => {
  return new Date(Date.now() + PH_OFFSET_MS - ONE_HOUR_MS).toISOString();
};

const formatDateForFilename = (date) => {
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const year = String(date.getFullYear()).slice(2, 4);
  return month + day + '.' + year;
};

const formatDateForTransaction = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return month + day + year;
};

module.exports = {
  getPhDateTimeString,
  getPreviousOneHourPhTime,
  getPhDateTime,
  getTodayTimestamp,
  formatDateForFilename,
  formatDateForTransaction
};
