const fs = require('fs');

const stringType = {
  type: 'string'
};

const intType = {
  type: 'integer'
};

const intTypeDefault = (defaultValue) => {
  return { ...intType, default: parseInt(defaultValue) };
};

const booleanTypeDefault = (defaultValue = false) => {
  return {
    type: 'boolean',
    default: defaultValue
  };
};

const stringTypeDefault = (defaultValue) => {
  return { ...stringType, default: defaultValue };
};

const getFromFile = (path) => fs.readFileSync(path, 'utf8');

module.exports = {
  intType,
  stringType,
  booleanTypeDefault,
  stringTypeDefault,
  intTypeDefault,
  getFromFile
};
