const { Client } = require('ssh2');
const fs = require('fs');
const {
  SFTP_HOST,
  SFTP_PORT,
  SFTP_USERNAME,
  SFTP_PRIVATE_KEY_PATH
} = require('@configs/env');
const { oneLineKeyToPemFormat } = require('@utils/keyConverter');

class SftpService {
  static instance;
  constructor() {
    const privateKey = fs.existsSync(SFTP_PRIVATE_KEY_PATH)
      ? fs.readFileSync(SFTP_PRIVATE_KEY_PATH)
      : null;

    const formattedPrivateKey = privateKey
      ? oneLineKeyToPemFormat(privateKey.toString())
      : null;

    this.connectionSettings = {
      host: SFTP_HOST,
      port: SFTP_PORT,
      username: SFTP_USERNAME,
      privateKey: formattedPrivateKey,
      readyTimeout: 40000
    };
  }

  static getInstance() {
    if (!SftpService.instance) {
      SftpService.instance = new SftpService();
    }
    return SftpService.instance;
  }

  upload(fileReadStream, targetRemotePath) {
    const conn = new Client();

    return new Promise((resolve, reject) => {
      conn.setMaxListeners(0);
      conn
        .on('ready', () => {
          conn.sftp((error, sftp) => {
            if (error) {
              this.#endConnection(conn);
              return reject({
                success: false,
                message: '[SFTP SVC] Error creating SFTP session',
                error
              });
            }

            try {
              const writeStream = sftp.createWriteStream(targetRemotePath);

              writeStream.on('close', () => {
                this.#endConnection(conn);
                resolve({
                  success: true,
                  message: '[SFTP SVC] File uploaded successfully'
                });
              });

              writeStream.on('error', (error) => {
                this.#endConnection(conn);
                reject({
                  success: false,
                  message: '[SFTP SVC] WriteStream: Error uploading file',
                  error
                });
              });

              fileReadStream.on('error', (error) => {
                this.#endConnection(conn);
                reject({
                  success: false,
                  message:
                    '[SFTP SVC] FileReadStream: Error reading source file',
                  error
                });
              });

              fileReadStream.pipe(writeStream);
            } catch (error) {
              this.#endConnection(conn);
              reject({
                success: false,
                message: '[SFTP SVC] Error uploading file',
                error
              });
            }
          });
        })
        .on('error', (error) => {
          this.#endConnection(conn);

          reject({
            success: false,
            message: '[SFTP SVC] Error connecting to SFTP server',
            error
          });
        })
        .connect(this.connectionSettings);
    });
  }

  #endConnection(connection) {
    if (connection && !connection.destroyed) {
      connection.end();
    }
  }
}

module.exports = SftpService.getInstance();
