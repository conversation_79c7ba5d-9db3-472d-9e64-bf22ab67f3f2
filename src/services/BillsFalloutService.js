const { name } = require('@root/package.json');
const logger = require('@configs/logger');
const {
  eventLogsRepository,
  settlementRepository,
  transactionLogsRepository,
  failedTransactionsRepository
} = require('@repositories');
const { writeDDBTransaction } = require('@utils/database');
const { createFalloutFileStream } = require('@utils/fileStream');
const { pgpEncryptStream } = require('@utils/pgp');
const { getPhDateTimeString, getPhDateTime } = require('@utils/date');
const {
  ACCOUNT_TYPE_MAPPING,
  PAYMENT_MODES,
  DEPOSIT_ACCOUNT_NUMBERS,
  getFalloutAccount,
  getCustomerType
} = require('@utils/fallout');
const { generateUuidv7 } = require('@utils/uuid');
const {
  formatDateForFilename,
  formatDateForTransaction
} = require('@utils/date');
const { chunkArray } = require('@utils/array');
const SftpClient = require('@services/SftpService');
const { FALLOUT_TARGET_PATH } = require('@configs/env');

class BillsFalloutService {
  static instance;
  constructor() {
    BillsFalloutService.instance = this;
    this.CHUNK_SIZE = 10; // DynamoDB TransactWrite limit
    // this.BATCH_SIZE = 100; // Processing batch size
  }

  static getInstance() {
    if (!BillsFalloutService.instance) {
      BillsFalloutService.instance = new BillsFalloutService();
    }
    return BillsFalloutService.instance;
  }

  async execute() {
    try {
      logger.info('[START] Bills Fallout Processing');

      // Step 1: Fetch failed transactions
      const failedTransactions = await this.getFailedTransactions();

      if (!failedTransactions || failedTransactions.length === 0) {
        logger.info('[INFO] No failed transactions found');
        return { success: true, processed: 0 };
      }

      logger.info(
        `[INFO] Found ${failedTransactions.length} failed transactions`
      );

      // Step 2: Process transactions by account type
      const processedAccounts =
        await this.processTransactionsByAccount(failedTransactions);

      // Step 3: Generate and upload fallout files
      const uploadResults =
        await this.generateAndUploadFiles(processedAccounts);

      // Step 4: Update transaction statuses (parallel with chunking)
      await this.updateTransactionStatuses(uploadResults);

      logger.info('[END] Bills Fallout Processing completed successfully');
      return {
        success: true,
        processed: failedTransactions.length,
        uploadResults
      };
    } catch (error) {
      logger.error('[ERROR] Bills Fallout Processing failed', {
        error: error.message,
        stack: error.stack
      });
      throw error;
    }
  }

  async getFailedTransactions() {
    logger.info('[INFO] Fetching failed transactions');
    const failedTransactions = [];
    let lastEvaluatedKey = null;

    do {
      const { items, lastEvaluatedKey: key } =
        await failedTransactionsRepository.getFailedPostings(lastEvaluatedKey);

      failedTransactions.push(...items);
      lastEvaluatedKey = key;
    } while (lastEvaluatedKey);

    return failedTransactions;
  }

  async processTransactionsByAccount(failedTransactions) {
    logger.info('[INFO] Processing transactions by account type');

    // Fetch all payment methods in parallel
    // Basically aggregate failedTrans and transLogs
    const transLogPromises = failedTransactions.map(async (transaction) => {
      try {
        const [{ paymentMethod }] =
          await transactionLogsRepository.getByPaymentId(transaction.paymentId);
        return { transaction, paymentMethod, success: true };
      } catch (error) {
        logger.error(
          `[ERROR] Failed to fetch payment method for paymentId: ${transaction.paymentId}`,
          error
        );
        return { transaction, paymentMethod: null, success: false };
      }
    });

    const transactionResults = await Promise.allSettled(transLogPromises);

    const falloutAccountMap = {};

    for (const result of transactionResults) {
      if (result.status === 'rejected') {
        logger.error('[ERROR] Failed to process transaction');
        logger.error({ err: result.reason });

        continue;
      }

      const { transaction, paymentMethod, success } = result.value;

      if (!success || !paymentMethod) {
        logger.error(
          `[ERROR] No payment method found for paymentId: ${transaction.paymentId}`
        );
        continue;
      }

      const {
        paymentId,
        channelId,
        accountNumber,
        amountValue,
        accountType,
        serviceNumber,
        accountName
      } = transaction;

      const accountTypeKey = ACCOUNT_TYPE_MAPPING[accountType] || 'globe';
      const paymentMethodKey = paymentMethod.includes('card')
        ? 'card'
        : paymentMethod;
      const customerType = getCustomerType(channelId);

      const {
        success: falloutSuccess,
        message,
        falloutAccount
      } = getFalloutAccount(falloutAccountMap, {
        accountType: accountTypeKey,
        customerType,
        paymentMethod: paymentMethodKey
      });

      if (!falloutSuccess) {
        logger.error('[ERROR] Failed to get fallout account', {
          error: message
        });
        logger.error(message);
        logger.error({
          paymentId,
          accountType: accountTypeKey,
          paymentMethod
        });
        continue;
      }

      falloutAccount.ids.push(paymentId);
      falloutAccount.rawData.push(transaction);

      const mappedData = this.mapTransactionData({
        paymentId,
        accountNumber,
        accountType: accountTypeKey,
        paymentMethod,
        amountValue,
        serviceNumber,
        accountName
      });
      falloutAccount.mappedData.push(mappedData);
    }

    return falloutAccountMap;
  }

  async generateAndUploadFiles(accounts) {
    logger.info('[INFO] Generating and uploading fallout files');

    const uploadResults = [];
    const currentDate = new Date();
    const dateStr = formatDateForFilename(currentDate);

    for (const [key, account] of Object.entries(accounts)) {
      if (account.mappedData.length === 0) {
        continue;
      }

      const [accountType, customerType, paymentMethod] = key.split('-');
      const filename = this.generateFilename(
        account.key,
        customerType,
        dateStr
      );

      const fileStream = createFalloutFileStream(account.mappedData);

      const uploadResult = await this.uploadToSftp(filename, fileStream);

      uploadResults.push({
        accountType,
        customerType,
        paymentMethod,
        transactionCount: account.mappedData.length,
        filename: uploadResult.filename,
        success: uploadResult.success,
        rawData: account.rawData
      });

      logger.info(
        `[INFO] Processed ${filename}: ${account.mappedData.length} transactions`
      );
    }

    return uploadResults;
  }

  async uploadToSftp(filename, fileStream) {
    logger.info(`[INFO] Uploading ${filename} to SFTP`);
    const encryptedStream = await pgpEncryptStream(fileStream);

    const { Readable } = require('stream');

    // pgp stream is now WebStream so we need to convert it to ReadableStream
    const readableStream = Readable.from(encryptedStream);
    const targetRemotePath = `${FALLOUT_TARGET_PATH}/${filename}`;
    await SftpClient.upload(readableStream, targetRemotePath);

    return { success: true, filename };
  }

  async updateTransactionStatuses(uploadResults) {
    logger.info('[INFO] Updating transaction statuses');

    // Filter successful uploads
    const successfulUploads = uploadResults.filter((ur) => ur.success);

    if (successfulUploads.length === 0) {
      logger.warn('[WARN] No successful uploads to update');
      return;
    }

    const updatePromises = [];

    for (const upload of successfulUploads) {
      const chunks = chunkArray(upload.rawData, this.CHUNK_SIZE);
      const chunksPromises = chunks.map((chunk) =>
        this.updateTransactionChunk(upload.filename, chunk)
      );
      updatePromises.push(...chunksPromises);
    }

    const results = await Promise.allSettled(updatePromises);

    const successful = results.filter(
      (result) => result.status === 'fulfilled'
    );
    const failed = results.filter((result) => result.status === 'rejected');

    if (failed.length > 0) {
      logger.error(
        `[ERROR] ${failed.length} transaction chunk updates failed:`
      );
      logger.error({
        err: failed.map((result) => ({
          error: result.reason || result.reason?.message
        }))
      });

      if (failed.length === results.length) {
        throw new Error(
          `All ${failed.length} transaction status updates failed`
        );
      } else {
        logger.warn(
          `[WARN] Partial failure: ${failed.length}/${results.length} transaction updates failed`
        );
      }
    }

    logger.info(
      `[INFO] Successfully updated all transaction statuses of ${successful.length}/${updatePromises.length} chunks`
    );
  }

  async updateTransactionChunk(filename, transactionsArray) {
    logger.info(
      `[INFO] Updating chunk with ${transactionsArray.map(
        (t) => `${t.paymentId}:${t.transactionId}`
      )}`
    );
    const phTimeNow = getPhDateTimeString();
    const operations = [];
    //[{data1}, {data2}]
    for (const transaction of transactionsArray) {
      const settlementParams = settlementRepository.buildUpdatePayload(
        {
          paymentId: transaction.paymentId,
          transactionId: transaction.transactionId
        },
        { status: 'POSTED_BATCHFILE', updateDateTime: phTimeNow }
      );

      const failedTransactionsParams =
        failedTransactionsRepository.buildUpdatePayload(
          {
            paymentId: transaction.paymentId,
            transactionId: transaction.transactionId
          },
          {
            status: 'POSTED_BATCHFILE',
            batchFileName: filename,
            updateDateTime: phTimeNow
          }
        );

      const eventLogsParams = eventLogsRepository.buildCreatePayload({
        paymentId: transaction.paymentId,
        eventId: generateUuidv7(),
        eventName: 'POSTING_BATCHFILE',
        eventSource: name,
        eventStatus: 'SUCCESS_POSTING_BATCHFILE',
        // eventDetails: transaction,
        createDateTime: phTimeNow
      });

      operations.push(
        ...[
          {
            operation: 'Update',
            params: settlementParams
          },
          {
            operation: 'Update',
            params: failedTransactionsParams
          },
          {
            operation: 'Put',
            params: eventLogsParams
          }
        ]
      );
    }

    await writeDDBTransaction(operations);

    logger.info(
      `[INFO] Updated chunk of ${transactionsArray.length} transactions`
    );
  }

  mapTransactionData({
    paymentId,
    accountNumber,
    accountType,
    paymentMethod,
    amountValue,
    serviceNumber,
    accountName
  }) {
    const currentDate = new Date();
    const transactionDate = formatDateForTransaction(currentDate);

    return {
      transactionDate,
      dealerName: accountName ? accountName.substring(0, 40) : 'N/A',
      customerAccountNo: accountNumber,
      transactionAmount: amountValue,
      depositAccountNumber: this.getDepositAccountNumber(
        accountType,
        paymentMethod
      ),
      serviceNumber,
      receiptingReferenceNumber: paymentId,
      debitCreditIndicator: 'C',
      cashCheckIndicator: '0',
      paymentMode: this.getPaymentMode(paymentMethod)
    };
  }

  //Get deposit account number based on account type and payment method
  getDepositAccountNumber(accountType, paymentMethod) {
    if (
      DEPOSIT_ACCOUNT_NUMBERS[paymentMethod] &&
      DEPOSIT_ACCOUNT_NUMBERS[paymentMethod][accountType]
    ) {
      return DEPOSIT_ACCOUNT_NUMBERS[paymentMethod][accountType];
    }
    return (
      DEPOSIT_ACCOUNT_NUMBERS.default[accountType] ||
      DEPOSIT_ACCOUNT_NUMBERS.default.globe
    );
  }

  getPaymentMode(paymentMethod) {
    return PAYMENT_MODES[paymentMethod] || PAYMENT_MODES.card;
  }

  generateFilename(key, customerType) {
    const manilaDate = getPhDateTime();
    const dateStr = formatDateForFilename(getPhDateTime());
    const hours = manilaDate.getHours();
    let batchNo;

    // 9am - 12:59pm
    if (hours >= 9 && hours < 13) {
      batchNo = '01';
      // 1pm - 4:59pm
    } else if (hours >= 13 && hours < 17) {
      batchNo = '02';
      // 5pm - 8:59pm
    } else if (hours >= 17 && hours < 21) {
      batchNo = '03';
      // 9pm - 8:59am
    } else {
      batchNo = '04';
    }

    if (customerType === 'corp') {
      return `${key}${dateStr}${batchNo}crp.txt`;
    } else {
      return `${key}${dateStr}${batchNo}`;
    }
  }
}

module.exports = BillsFalloutService.getInstance();
