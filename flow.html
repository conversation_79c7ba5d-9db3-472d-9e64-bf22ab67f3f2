<!DOCTYPE html>
<html lang="en">

<body>
    <h3>[Payments] - Bills Payment Posting</h3>
    <hr />
    <pre class="mermaid">
      sequenceDiagram
        Callback SVC ->> Xendit : Call GetCharge API using credit_card_charge_id
        Xendit -->> Callback SVC: Sends response
        Note right of Callback SVC: status: "CAPTURED", "FAILED" <br><br/>Update transLog table <br>"MID"<br>"Payment Method"<br>"Installment Details for CCI"


        Callback SVC-->>Channel: Send status to the channel via callback
        Note left of Channel: status: "CARD_AUTHORISED", "CARD_REFUSED", <br/>including other details
        
        alt CARD_AUTHORISED
        Callback SVC -->> Confluent Kafka: Publish message to kafka topic for Payment Posting
        Bills Payment Core -->> Confluent Kafka: Consume message from the topic
        Bills Payment Core ->> HIP: Call GetAccountInfo API to get account details
        HIP -->> Bills Payment Core: Send response
        Bills Payment Core ->> HIP: Call PaymentPosting API to post the transaction
        HIP -->> Bills Payment Core: Send response
            alt SUCCESS Posting
            Note right of Bills Payment Core: Update the status to PAYMENT_POSTING
            else FAILED Posting
            Note right of Bills Payment Core: Update the status to PAYMENT_POSTING_FAILED for batch processing
            end
        else CARD_REFUSED
        Note left of Bills Payment Core: No Action
        end
        
        
    </pre>
    <script type="module">
        import mermaid from "https://cdn.jsdelivr.net/npm/mermaid@11/dist/mermaid.esm.min.mjs";
    </script>
</body>

</html>