version: '3.8'

services:
  app:
    build:
      context: ..
      dockerfile: .devcontainer/Dockerfile
    volumes:
      - ..:/home/<USER>/app:cached
      - /home/<USER>/app/node_modules
    command: sleep infinity
    environment:
      - NODE_ENV=local
    networks:
      - app-network
      - localstack-network
    depends_on:
      - sftp

  sftp:
    image: atmoz/sftp:latest
    ports:
      - '2222:22'
    volumes:
      # mount the whole sftpuser folder so atmoz/sftp can create subdirectories
      # and we don't need to chown
      # can also remove this mount if there's no need to view the uploads on the host machine
      - ./sftp/sftp_data:/home/<USER>
      # public key mount so the sftp can accept connections
      - ./sftp/ssh_keys/id_rsa.pub:/home/<USER>/.ssh/keys/id_rsa.pub:ro
      # identification key for the sftp server (not important in local setup)
      # - ./sftp/ssh_host_keys:/etc/ssh/keys
    environment:
      # user sftpuser is created with UID:GID of 1000:1000
      # upload is also created owned by sftpuser:1000
      - SFTP_USERS=sftpuser::1000:1000:upload
    networks:
      - app-network

networks:
  app-network:
    driver: bridge
  localstack-network:
    external: true
# volumes:
#   sftp_data:
