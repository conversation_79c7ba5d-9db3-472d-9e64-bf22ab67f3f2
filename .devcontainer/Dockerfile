ARG NODE_VERSION=22.14.0
FROM node:${NODE_VERSION}-alpine AS base

# Install runtime dependencies
RUN apk --no-cache add \
    python3 \
    make \
    g++ \
    lz4-dev \
    cyrus-sasl \
    ca-certificates \
    git

# Set the working directory
WORKDIR /home/<USER>/app

RUN mkdir -p node_modules && chown -R node:node node_modules

# Switch to the non-root user
USER node

# # Copy package files
COPY --chown=node:node package*.json ./
RUN npm install
COPY --chown=node:node . .
