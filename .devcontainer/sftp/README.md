# SFTP Devcontainer Setup

This directory contains the configuration for the SFTP server used in the devcontainer environment.

## Overview

The SFTP server uses the `atmoz/sftp` Docker image with SSH key authentication for secure file transfers. The setup script automatically generates all required keys.

## Configuration

- **Image**: `atmoz/sftp:latest`
- **Port**: 2222 (mapped from container port 22)
- **Username**: sftpuser
- **Authentication**: SSH key-based (auto-generated)
- **Upload Directory**: `/home/<USER>/upload`

## Quick Start

1. Run the setup script to generate all required keys:

   ```bash
   ./devcontainer/sftp/setup-sftp.sh
   ```
2. Start the devcontainer:

   ```bash
   docker-compose up -d
   ```

3. Test the SFTP connection:
   ```bash
   sftp -i .devcontainer/sftp/ssh_keys/id_rsa -P 2222 sftpuser@localhost
   ```

## Generated Files

The setup script will create:

### SFTP Keys

- `ssh_keys/id_rsa` - Private SSH key for SFTP authentication
- `ssh_keys/id_rsa.pub` - Public SSH key for SFTP authentication
- `known_hosts` - SSH known hosts file

### PGP Keys

- `../.pgp/public_key.asc` - PGP public key (armored format)
- `../.pgp/private_key.asc` - PGP private key (armored format)

### Data Directories

- `sftp_data/` - SFTP server data directory
- `sftp_data/upload/` - Upload directory for SFTP transfers

## Environment Variables

The following environment variables are configured in `.env.example`:

- `SFTP_HOST=localhost`
- `SFTP_PORT=2222`
- `SFTP_USERNAME=sftpuser`
- `SFTP_PRIVATE_KEY_PATH=.devcontainer/sftp/ssh_keys/id_rsa`

- `PGP_PUBLIC_KEY_PATH=.pgp/public_key.asc`

## Testing Connection

```bash
# Using sftp command
sftp -i .devcontainer/sftp/ssh_keys/id_rsa -P 2222 sftpuser@localhost

# Using scp to upload a file
scp -i .devcontainer/sftp/ssh_keys/id_rsa -P 2222 localfile.txt sftpuser@localhost:upload/
```

## Notes

- All keys are generated automatically - no manual key management required
- Keys are gitignored for security
- The setup script is idempotent - safe to run multiple times
- PGP keys are generated without passphrase for development use
