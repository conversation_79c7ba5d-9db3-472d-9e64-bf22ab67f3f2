#!/bin/bash

set -e

echo "Setting up SFTP devcontainer and generating required keys..."

# Create necessary directories
echo "Creating directories..."
mkdir -p .devcontainer/sftp/ssh_keys
mkdir -p .devcontainer/sftp/sftp_data/upload
mkdir -p .pgp

# Generate SSH key pair for SFTP if it doesn't exist
if [ ! -f .devcontainer/sftp/ssh_keys/id_rsa ]; then
    echo "Generating SSH key pair for SFTP..."
    ssh-keygen -t rsa -b 4096 -f .devcontainer/sftp/ssh_keys/id_rsa -N "" -C "sftpuser@devcontainer"
    echo "SSH key pair generated successfully"
else
    echo "SSH key pair already exists, skipping generation"
fi

# Generate PGP key pair if it doesn't exist
if [ ! -f .pgp/public_key.asc ]; then
    echo "Generating PGP key pair..."
    
    # Create a temporary GPG home directory
    GNUPGHOME=$(mktemp -d)
    export GNUPGHOME
    
    # Generate PGP key pair
    cat > "$GNUPGHOME/gen-key-config" <<EOF
%echo Generating PGP key for development...
Key-Type: RSA
Key-Length: 4096
Subkey-Type: RSA
Subkey-Length: 4096
Name-Real: SFTP Dev User
Name-Email: <EMAIL>
Expire-Date: 0
Passphrase: 
%commit
%echo PGP key generation complete
EOF

    gpg --batch --generate-key "$GNUPGHOME/gen-key-config"
    
    # Export the keys
    gpg --armor --export <EMAIL> > .pgp/public_key.asc
    gpg --armor --export-secret-keys <EMAIL> > .pgp/private_key.asc
    
    # Clean up temporary directory
    rm -rf "$GNUPGHOME"
    
    echo "PGP key pair generated successfully"
else
    echo "PGP key pair already exists, skipping generation"
fi

# Set proper permissions for SSH keys
echo "Setting proper permissions..."
chmod 600 .devcontainer/sftp/ssh_keys/id_rsa
chmod 644 .devcontainer/sftp/ssh_keys/id_rsa.pub
chmod 600 .pgp/private_key.asc
chmod 644 .pgp/public_key.asc

# Set proper ownership for SFTP data directory
chown -R 1000:1000 .devcontainer/sftp/sftp_data 2>/dev/null || echo "Note: Could not set ownership (run as root if needed)"

echo ""
echo "✅ SFTP and PGP setup complete!"
echo ""
echo "Generated files:"
echo "  📁 SFTP SSH Keys:"
echo "    - .devcontainer/sftp/ssh_keys/id_rsa (private)"
echo "    - .devcontainer/sftp/ssh_keys/id_rsa.pub (public)"
echo "  📁 PGP Keys:"
echo "    - .pgp/public_key.asc (public, armored)"
echo "    - .pgp/private_key.asc (private, armored)"
echo "  📁 Data Directories:"
echo "    - .devcontainer/sftp/sftp_data/upload/"
echo ""
echo "Next steps:"
echo "1. Start devcontainer: docker-compose up -d"
echo "2. Wait for services to be ready"
echo "3. Generate known_hosts: ssh-keyscan -p 2222 localhost > .devcontainer/sftp/known_hosts"
echo "4. Test connection: sftp -i .devcontainer/sftp/ssh_keys/id_rsa -P 2222 sftpuser@localhost"